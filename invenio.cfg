"""
InvenioRDM settings for My Site project.

This file was automatically generated by 'invenio-cli init'.

For the full list of settings and their values, see
https://inveniordm.docs.cern.ch/reference/configuration/.
"""

from datetime import datetime
from invenio_i18n import lazy_gettext as _


def _(x):  # needed to avoid start time failure with lazy strings
    return x

# Flask
# =====
# See https://flask.palletsprojects.com/en/1.1.x/config/

# Define the value of the cache control header `max-age` returned by the server when serving
# public files. Files will be cached by the browser for the provided number of seconds.
# See flask documentation for more information:
# https://flask.palletsprojects.com/en/2.1.x/config/#SEND_FILE_MAX_AGE_DEFAULT
SEND_FILE_MAX_AGE_DEFAULT = 300

# SECURITY WARNING: keep the secret key used in production secret!
# Do not commit it to a source code repository.
# TODO: Set
SECRET_KEY="CHANGE_ME"

# Since <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> route all requests no matter the host header
# provided, the trusted hosts variable is set to localhost. In production it
# should be set to the correct host and it is strongly recommended to only
# route correct hosts to the application.
TRUSTED_HOSTS = ['0.0.0.0', 'localhost', '127.0.0.1']


# Flask-SQLAlchemy
# ================
# See https://flask-sqlalchemy.palletsprojects.com/en/2.x/config/

# TODO: Set
SQLALCHEMY_DATABASE_URI="postgresql+psycopg2://my-site:my-site@localhost/my-site"


# Invenio-App
# ===========
# See https://invenio-app.readthedocs.io/en/latest/configuration.html

APP_DEFAULT_SECURE_HEADERS = {
    'content_security_policy': {
        'default-src': [
            "'self'",
            'data:', # for fonts
            "'unsafe-inline'",  # for inline scripts and styles
            "blob:",            # for pdf preview
            # Add your own policies here (e.g. analytics)
        ],
        'script-src': [
            "'self'", "blob:", "'wasm-unsafe-eval'"  # for WASM-based workers
            # Multipart file uploads use a Web Worker running `hash-wasm` to compute content checksums
            # (e.g., MD5) of uploaded parts. This requires both 'blob:' and 'wasm-unsafe-eval' enabled in `script-src`.
        ],
    },
    'content_security_policy_report_only': False,
    'content_security_policy_report_uri': None,
    'force_file_save': False,
    'force_https': True,
    'force_https_permanent': False,
    'frame_options': 'sameorigin',
    'frame_options_allow_from': None,
    'session_cookie_http_only': True,
    'session_cookie_secure': True,
    'strict_transport_security': True,
    'strict_transport_security_include_subdomains': True,
    'strict_transport_security_max_age': 31556926,  # One year in seconds
    'strict_transport_security_preload': False,
}


# Flask-Babel
# ===========
# See https://python-babel.github.io/flask-babel/#configuration

# Default locale (language)
BABEL_DEFAULT_LOCALE = 'en'
# Default time zone
BABEL_DEFAULT_TIMEZONE = 'Europe/Zurich'


# Invenio-I18N
# ============
# See https://invenio-i18n.readthedocs.io/en/latest/configuration.html

# Other supported languages (do not include BABEL_DEFAULT_LOCALE in list).
I18N_LANGUAGES = [
    # ('de', _('German')),
    # ('tr', _('Turkish')),
]


# Invenio-Theme
# =============
# See https://invenio-theme.readthedocs.io/en/latest/configuration.html

# Name used in header and UI
THEME_SITENAME = "My Site"
# Frontpage title
THEME_FRONTPAGE_TITLE = "My Site"
# Header logo
THEME_LOGO = 'images/invenio-rdm.svg'


# Invenio-App-RDM
# ===============
# See https://github.com/inveniosoftware/invenio-app-rdm/blob/master/invenio_app_rdm/config.py

# Instance's theme entrypoint file. Path relative to the ``assets/`` folder.
INSTANCE_THEME_FILE = './less/theme.less'

# Email address for administrator emails (like file checksum alerts)
APP_RDM_ADMIN_EMAIL_RECIPIENT = "<EMAIL>"

# Default values for the deposit form
APP_RDM_DEPOSIT_FORM_DEFAULTS = {
    "publication_date": lambda: datetime.now().strftime("%Y-%m-%d"),
    "rights": [
        {
            "id": "cc-by-4.0",
            "title": "Creative Commons Attribution 4.0 International",
            "description": ("The Creative Commons Attribution license allows "
                            "re-distribution and re-use of a licensed work "
                            "on the condition that the creator is "
                            "appropriately credited."),
            "link": "https://creativecommons.org/licenses/by/4.0/legalcode",
        }
    ],
    "publisher": "My Site",
}

APP_RDM_DEPOSIT_FORM_AUTOCOMPLETE_NAMES = 'search' # "search_only" or "off"

# Invenio-Records-Resources
# =========================
# See https://github.com/inveniosoftware/invenio-records-resources/blob/master/invenio_records_resources/config.py

# TODO: Set with your own hostname when deploying to production
SITE_UI_URL = "https://127.0.0.1:5000"

SITE_API_URL = "https://127.0.0.1:5000/api"

# Invenio-RDM-Records
# ===================
# See https://inveniordm.docs.cern.ch/customize/dois/
DATACITE_ENABLED = False
DATACITE_USERNAME = ""
DATACITE_PASSWORD = ""
DATACITE_PREFIX = ""
DATACITE_TEST_MODE = True
DATACITE_DATACENTER_SYMBOL = ""

# Authentication - Invenio-Accounts and Invenio-OAuthclient
# =========================================================
# See: https://inveniordm.docs.cern.ch/customize/authentication/

# Invenio-Accounts
# ----------------
# See https://github.com/inveniosoftware/invenio-accounts/blob/master/invenio_accounts/config.py
ACCOUNTS_LOCAL_LOGIN_ENABLED = True  # enable local login
SECURITY_REGISTERABLE = True  # local login: allow users to register
SECURITY_RECOVERABLE = True  # local login: allow users to reset the password
SECURITY_CHANGEABLE = True  # local login: allow users to change psw
SECURITY_CONFIRMABLE = True  # local login: users can confirm e-mail address
SECURITY_LOGIN_WITHOUT_CONFIRMATION = False # require users to confirm email before being able to login

# Invenio-OAuthclient
# -------------------
# See https://github.com/inveniosoftware/invenio-oauthclient/blob/master/invenio_oauthclient/config.py

OAUTHCLIENT_REMOTE_APPS = {}  # configure external login providers

from invenio_oauthclient.views.client import auto_redirect_login
ACCOUNTS_LOGIN_VIEW_FUNCTION = auto_redirect_login  # autoredirect to external login if enabled
OAUTHCLIENT_AUTO_REDIRECT_TO_EXTERNAL_LOGIN = False  # autoredirect to external login

# Invenio-UserProfiles
# --------------------
USERPROFILES_READ_ONLY = False  # allow users to change profile info (name, email, etc...)

# OAI-PMH
# =======
# See https://github.com/inveniosoftware/invenio-oaiserver/blob/master/invenio_oaiserver/config.py

OAISERVER_ID_PREFIX = "my-site.com"
"""The prefix that will be applied to the generated OAI-PMH ids."""
OAISERVER_ADMIN_EMAILS = [
    "<EMAIL>",
]

# Invenio-Search
# --------------

SEARCH_INDEX_PREFIX = "my-site-"

# Invenio-Administration
# ----------------------

from invenio_app_rdm import __version__
ADMINISTRATION_DISPLAY_VERSIONS = [
    ("invenio-app-rdm", f"v{__version__}"),
    ("my-site", "v1.0.0"),
]
