/*

████████╗██╗  ██╗███████╗███╗   ███╗███████╗███████╗
╚══██╔══╝██║  ██║██╔════╝████╗ ████║██╔════╝██╔════╝
   ██║   ███████║█████╗  ██╔████╔██║█████╗  ███████╗
   ██║   ██╔══██║██╔══╝  ██║╚██╔╝██║██╔══╝  ╚════██║
   ██║   ██║  ██║███████╗██║ ╚═╝ ██║███████╗███████║
   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝     ╚═╝╚══════╝╚══════╝

*/

/*******************************
        Theme Selection
*******************************/

/* To override a theme for an individual element
   specify theme name below
*/

/* Global */
@site        : 'rdm';
@reset       : 'default';

/* Elements */
@button      : 'rdm';
@container   : 'rdm';
@divider     : 'rdm';
@flag        : 'rdm';
@header      : 'rdm';
@icon        : 'default';
@image       : 'rdm';
@input       : 'rdm';
@label       : 'rdm';
@list        : 'rdm';
@loader      : 'rdm';
@placeholder : 'rdm';
@rail        : 'rdm';
@reveal      : 'rdm';
@segment     : 'rdm';
@step        : 'rdm';

/* Collections */
@breadcrumb  : 'rdm';
@form        : 'rdm';
@grid        : 'rdm';
@menu        : 'rdm';
@message     : 'rdm';
@table       : 'rdm';

/* Modules */
@accordion   : 'rdm';
@checkbox    : 'rdm';
@dimmer      : 'rdm';
@dropdown    : 'rdm';
@embed       : 'rdm';
@modal       : 'rdm';
@nag         : 'rdm';
@popup       : 'rdm';
@progress    : 'rdm';
@rating      : 'rdm';
@search      : 'rdm';
@shape       : 'rdm';
@sidebar     : 'rdm';
@sticky      : 'rdm';
@tab         : 'rdm';
@transition  : 'default';

/* Views */
@ad          : 'rdm';
@card        : 'rdm';
@comment     : 'rdm';
@feed        : 'rdm';
@item        : 'rdm';
@statistic   : 'rdm';

/*******************************
            Folders
*******************************/

/* Path to theme packages */
@themesFolder : '~semantic-ui-less/themes';

/* Path to site override folder */
@siteFolder  : '../../less/site';
@imagesFolder : '../../images';


/*******************************
         Import Theme
*******************************/

@import (multiple) "themes/rdm/theme.less";

@fontPath : "../../../themes/@{theme}/assets/fonts";

/* End Config */
