user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    types {
        # The default MIME types file doesn't assign "application/javascript"
        # to "*.mjs" files (ECMAScript modules)
        application/javascript js mjs;
    }

    # Standard log format
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # Request tracing log format - includes request id, session id, user id,
    # and request timing.
    log_format trace '$remote_addr - [$time_local] "$request" '
                 '$status  $body_bytes_sent "$http_referer"  '
                 '"$http_user_agent" "$http_x_forwarded_for" $request_id '
                 '$msec $request_time '
                 '$upstream_http_x_session_id $upstream_http_x_user_id';

    access_log  /var/log/nginx/access.log  trace;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;

    keepalive_timeout  65;

    gzip on;
    gzip_disable "msie6";
    gzip_http_version 1.1;
    gzip_comp_level 5; # or anything between 4-6
    gzip_min_length 100;
    gzip_proxied any;
    # We may need more mime-types here (eg. 'application/x-bibtex')
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/octet-stream
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-javascript
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        application/xml+rss
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/javascript
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy
        text/xml;
    gzip_vary on;

    include /etc/nginx/conf.d/*.conf;
}
