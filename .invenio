[cli]
flavour = RDM
logfile = /logs/invenio-cli.log

[cookiecutter]
project_name = My Site
project_shortname = my-site
package_name = my_site
project_site = my-site.com
author_name = CERN
author_email = <EMAIL>
year = 2025
database = postgresql
search = opensearch2
file_storage = local
development_tools = yes
site_code = yes
use_reduced_vocabs = no
_template = https://github.com/inveniosoftware/cookiecutter-invenio-rdm.git
_output_dir = /root
_repo_dir = /root/.cookiecutters/cookiecutter-invenio-rdm
_checkout = v13.0

[files]
site = {'.gitkeep': 'b9c7aa25b6e5556b9d55374bf6df2c3074aaedc4c0dcee4ac25b2237255891c1', 'pyproject.toml': 'e1acd833b38f03f26cc31439246cbeeb82e2edf56c751f28c3247cb44e95caca', 'my_site': {'templates': {'semantic-ui': {'my_site': {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}}}, 'webpack.py': 'a697055250990c0c349125b223cab57feb8d37d984170f4060ad43746c268777', 'views.py': '9edb8e6d954d44cf538fb263a7bbc8f9d97e073c98dd5a7a5ae25edde98698f5', '__init__.py': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'assets': {'semantic-ui': {'less': {'my_site': {'.gitkeep': 'de96a61fec5b6b168bced6dda58bd11e353f618ae9bee45aad9253cb0d6c5617'}}, 'js': {'my_site': {'.gitkeep': '3253a6fe7fc74a1279fb374e67ffd3383eded63cd28c4877afd77dffc8cc5da4'}}}}}, 'setup.py': '843ac26c38a41abae578250bc0f9419194b320a0f67327d941037a4268f6cfe7', 'tests': {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}, 'setup.cfg': '511d0cfe1f729aed5f588a07c998173af009eee222a872028e782d736eebe733'}
invenio.cfg = 1eac57ff5e051728be9942328e1f940f7927abce20b449ba1d7d338ed9dae8db
templates = {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}
.gitignore = af1182c70588e583daeb35809e6cdfe3408425d438c3ceb282b1f4b8360290e2
translations = {'babel.ini': '3fb6bff73460aa031d4f90fc1e1d16235cab84d2385c9831efacb2ce32e5409b'}
docker = {'uwsgi': {'uwsgi_ui.ini': 'aeb0797a1fadb462e0ed208c979d5152cc58a85958d4de9c4e989637c70b9005', 'uwsgi_rest.ini': 'c36fc5021baf15f375b3b6c825f7753e5d166319c5fe38a85b604391ce5b37b6'}, 'pgadmin': {'servers.json': '156a04fe13c253678a3e3af4a6cb03f1e1cc9154182cad235eed4f4f46165833'}, 'nginx': {'test.crt': 'd4750f0318b65a2dbd0c0be6534d3ed4d885000ee096af76c12bc82b8124fbd5', 'Dockerfile': '541326766beaba2e8492255c99e11aa76640d71329ceab5bcab1c8d6a979bd5c', 'nginx.conf': '7ba045f9e5c94cbbe96653c0b2fdc0b58951642d488d5fcae1ace3eeca9253c0', 'test.key': 'd800b4fabf3d7f3b5fa8bfe73dcf1836dad1f2e53052c745d4e48f277eaf6f81', 'conf.d': {'default.conf': '360a483a89dbea713f4138b8115d06558897f2419afef1347b50cd800ea16ccc'}}}
app_data = {'pages': {'.gitkeep': '22818f8c9f68fb11b7ca893ad1c082f844ae9f691f082d2174e32738d9ae6114'}, 'vocabularies': {'cc_licences.csv': '19a8bfd2488c7adb90d1dbe1c70f57cf2e0a737cb11814cc814f917072843de7', 'subjects_oecd_fos.yaml': 'eef38714c506d956193c3be17e6004049525d252bf22424f4804dfc1ad46db44', 'languages.yaml': 'd3bdb59940e8e6cd2401785ad55733f5fc6a1fb0bb0a2005a8fe4e5148e5f286', 'names.yaml': '6dc80984186ec960bcf53fbb6c482554d0aaf386d345bf82f504fdbd896e5848'}, 'README.md': 'd58978488626bdeddc791a15a39e341a4ed06f202b84d0c0d4aa9ef49481c6d9', 'vocabularies.yaml': 'e860d2136df803dc05e67c8f1c32bc58aa4bdca348dcc2daf7aaa4aee67ecfbf'}
dockerfile = 44e5c207cf3e82cddda086f73e6fa5f3bacfc06d11f55a95f954d744a773c605
pipfile = 1523ed5b310092d7f4539fd459e9fa13381dc062d95e5044cdd0957c85379429
docker-services.yml = 95122606fbabfa9a7a91e1d654fb9ec30f4e85d740cb23e445c23f904e49bbdf
docker-compose.full.yml = c9461c976b4be84a23daba022d37dac3a91015b1551e96eae1f28a3f2cf4e9d1
readme.md = 4115ab294c7a85ef212c38f180967dd9d9c0f39b769f6363944e831569ea4c9f
.dockerignore = 21241bc12cdf4ac5f209d6b191adc8f25eb89af93ce5c4c3f72c86661162d7b5
docker-compose.yml = 2073d3075bb72b287e0d700ad7f798c27ff2bad57bd41613fc29391b761665d9
assets = {'less': {'site': {'globals': {'site.variables': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'site.overrides': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'}}, 'theme.config': 'ee68441b779400585a3a1ed4445ab5727e3a47a09aa708976dd506c0e081f0d8'}, 'templates': {'search': {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}, 'custom_fields': {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}}, 'js': {'invenio_app_rdm': {'overridableRegistry': {'mapping.js': '7fb63c1b801d11f1c1bb9b7d5cad6662ae475210df2bbba6919cda402684590a'}}}}
static = {'images': {'invenio-rdm.svg': '50f09b4d83244a69c58f44aa436a3fa097726d9b47fd0e0782ea92d31f854b65', 'logo-invenio-white.svg': '5af2c7b67a09798cba3d50cc2e811ca9fd2ff73b40d9d2a52a97237b9ed44008'}}

